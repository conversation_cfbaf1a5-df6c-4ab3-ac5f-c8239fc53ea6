# Yocto Build Scripts for AM64xx Sitara Hardware

This README describes the usage of the scripts and features included in the project for AM64xx Sitara hardware and PLCnext framework integration.

### 1. Preparation build installations
If you prepare your build machine for a first time, you need to install the following applications before other steps bellow:

```
$ sudo apt-get update

# Install packages required for builds
$ sudo apt-get -f -y install \
git build-essential diffstat texinfo gawk chrpath socat doxygen \
dos2unix python3 bison flex libssl-dev u-boot-tools mono-devel \
mono-complete curl python3-distutils repo pseudo python3-sphinx \
g++-multilib libc6-dev-i386 jq git-lfs pigz zstd liblz4-tool \
cpio file zstd lz4 doxygen coreutils tree graphviz openssh-client git-lfs libxapian-dev pahole
```

**Note: If PLCnext updates must be used after 01.12.2024, must be checked "doxygen" version on host build machine. It must be 1.12.0 or 1.13.1. If the version is older then the following procedure must be processed:**
```
1. Update Doxygen version - remove old one first:
$ sudo apt remove --purge doxygen
$ sudo apt autoremove

2. Use Precompiled Binaries:
Download page - [https://www.doxygen.nl/download.html](https://www.doxygen.nl/download.html)
$ tar -xvzf doxygen-1.13.1.linux.bin.tar.gz
$ sudo cp doxygen-1.13.1/bin/* /usr/bin/
$ sudo mv doxygen-1.13.1/man/man1/* /usr/local/share/man/man1/
$ sudo mandb

3. Check if doxygen version was updated:
$ doxygen --version

4. If doxygen is available but libxapian.so.30 missing:
$ ldd /usr/bin/doxyindexer
If the result is (for example):
sergey@bg0smyv:~/temp$ ldd /usr/bin/doxyindexer
        linux-vdso.so.1 (0x00007ffe935b1000)
        libxapian.so.30 => not found
        libstdc++.so.6 => /lib/x86_64-linux-gnu/libstdc++.so.6 (0x000074a171000000)
        libgcc_s.so.1 => /lib/x86_64-linux-gnu/libgcc_s.so.1 (0x000074a171242000)
        libc.so.6 => /lib/x86_64-linux-gnu/libc.so.6 (0x000074a170c00000)
        libm.so.6 => /lib/x86_64-linux-gnu/libm.so.6 (0x000074a170f19000)
        /lib64/ld-linux-x86-64.so.2 (0x000074a1712a6000)

On the host must be installed 'libxapian-dev' also.
$ sudo apt update
$ sudo apt install libxapian-dev
```

### 2. checkout_sources.sh usage

-  **Without parameters**:

Running the script without parameters will list all available configuration files in the `configs/processor-sdk` directory. These configuration files specify the layers required for different Yocto builds.

``` Example

vboxuser@Ubuntu:~/midrange$ ./scripts/checkout_sources.sh

[WARRNING] Please specify a config file for next processing!

Available config files in configs/processor-sdk are:

processor-sdk-09.00.00-config.txt

processor-sdk-am64xx-config-01.00.txt

```

-  **With a parameter**:

When run with a parameter, which must be the name of a configuration file from the `configs/processor-sdk` directory, the script will clone all the necessary Yocto layers as defined in the specified configuration file.

**Example**:
```bash
./checkout_sources.sh  processor-sdk-am64xx-config-01.00.txt
```

### 2. Information about configurations in configs/processor-sdk
-  **processor-sdk-09.00.00-config.txt**
Clone all layers needed for a basic core tisdk sitara image

-  **processor-sdk-am64xx-config-01.00.txt**
Clone all layers needed for a am64xx sitara image including plcnext framework


### 3. build_am64xx.sh usage

The scripts is run without parameters and it builds tisdk base image for am64xx sitara hardware. For building this image, processor-sdk-09.00.00-config.txt configuration need to be used.


### 4. build_am64xx_pxc.sh usage

The scripts is run without parameters and it builds image for am64xx evaluation hardware which includes plcnext framework. For building this image, processor-sdk-am64xx-config-01.00.txt configuration need to be used.


### 5. build_midrange_pxc.sh usage

The scripts is run without parameters and it builds image for am64xx MIDRANGE hardware which includes plcnext framework. For building this image, processor-sdk-am64xx-config-01.00.txt configuration need to be used.

### 6. build_midrange_pxc_emmc4g_usb4g.sh usage
This script builds two separate images for the **am64xx MIDRANGE hardware**:

- One for **eMMC (4GB)** variant
- One for **USB (4GB)** variant

These images include the **PLCnext framework**, and are based on the `pxc-image-base` and `flasher-image-base` recipes, respectively. For building this image, processor-sdk-am64xx-config-01.00.txt configuration need to be used.

Before building and flashing, please follow these **Target preparation steps** if the target has been previously flashed:

#### ⚠️ Target preparation (U-Boot mode)

1. Power ON the target and press **Enter repeatedly** to enter **U-Boot** mode.
2. Run the following commands in U-Boot to erase the first sectors of the memory:
```
mmc dev 0 1
mmc erase 0 1
```
⚠️ **Note:** The USB dongle must **NOT** be connected during this step.

3. Power OFF the target.
4. Insert the **pre-flashed USB dongle** with the new image.
5. Power ON the target to begin flashing.

### 7. build_midrange_emmc4g.sh usage

This script builds a **RAUC update bundle** (`pxc-bundle-base`) and a **root filesystem image** (`pxc-image-base`) for the **AM64xx MIDRANGE hardware (eMMC 4GB variant)**. For building this image, processor-sdk-am64xx-config-01.00.txt configuration need to be used.

## Exam build procedure of a yocto image includes plcnext

1.  **Make sure that all rights are avilable for succesfull cloning of the following repos:**

https://gitlab.festo.company/festo/yocto/ea/controls/p30/poc/midrange.git

  

https://gitlab.festo.company/festo/yocto/third-party/poky.git

https://gitlab.festo.company/festo/yocto/third-party/meta-openembedded.git

https://gitlab.festo.company/festo/yocto/third-party/bitbake.git

https://gitlab.festo.company/festo/yocto/third-party/meta-arm.git

https://gitlab.festo.company/festo/yocto/third-party/meta-security.git

https://gitlab.festo.company/festo/yocto/third-party/meta-rauc.git

https://gitlab.festo.company/festo/yocto/third-party/meta-virtualization.git

https://gitlab.festo.company/festo/yocto/third-party/pxc/meta-pxc.git

https://gitlab.festo.company/festo/yocto/third-party/pxc/meta-arp.git

https://gitlab.festo.company/festo/yocto/third-party/pxc/meta-hardware.git

https://gitlab.festo.company/festo/yocto/ea/controls/meta/meta-festo.git

https://gitlab.festo.company/festo/yocto/ea/controls/p30/meta-festo-midrange.git

  

2.  **Clone midrange repo**

```

git clone https://gitlab.festo.company/festo/yocto/ea/controls/p30/poc/midrange.git

```

3.  **Clone layers needed for yocto sitara build**

For that reason must be run **checkout_sources.sh** script from _../midrange/scripts_ with a parameter which is an availbe config file from _../midrange/configs/processor-sdk_.

```
vboxuser@Ubuntu:~midrange$ ./scripts/checkout_sources.sh processor-sdk-am64xx-config-01.00.txt
```

4.  **Image building**

For that reason **build_am64xx_pxc.sh** is run only.

```
vboxuser@Ubuntu:~midrange$ ./scripts/build_am64xx_pxc.sh
```

The script sets up environment for the build process and starts with **pxc-image-base** image building.

5.  **Image flashing**

After build finish images can be found in _../midrange/tisdk/build/deploy-ti/images/am64xx-evm_.
Before to flash the image check which device represent your microSD card /dev/sdX
If your microSD card is mounted un-mount it! Could use _**lsblk**_ to check:

```
$ lsblk
```
- unmount device:
```

$ umount /dev/sdX1
$ umount /dev/sdX2

```
- flashing with _**bmaptool**_
```

sudo bmaptool copy --nobmap pxc-image-base-am64xx-evm.rootfs-20240916190927.wic.xz /dev/sdX

```
The image from the last example is exemplary so you need to find your image with the correct timestamp in build/deploy-ti/images/am64xx-evm directory.

## Build and install SDK
1.  **Build SDK for am64xx evaluation board**
There are two options:
- manual building of sdk
```
$ cd ../tisdk/build
$ source conf/setenv
$ bitbake pxc-image-base -c populate_sdk
```
- run the script from ../midrange/scripts/build_am64xx_pxc_sdk.sh
```
./scripts/build_am64xx_sdk_pxc.sh
```

After sussefull installation, generated sdk can be found in deploy directory in sdk folder.
```
$:~/Midlayer/p30_gitlab_midrange/midrange/tisdk/build/deploy-ti/sdk$ ll
total 1515012
drwxr-xr-x 2 <USER> <GROUP> 4096 окт  1 10:10 ./
drwxr-xr-x 4 <USER> <GROUP> 4096 окт  1 10:10 ../
-rw-r--r-- 2 <USER> <GROUP> 11731 окт  1 09:53 oecore-pxc-image-base-x86_64-aarch64-am64xx-evm-toolchain-2024.8.host.manifest
-rw-r--r-- 2 <USER> <GROUP> 1207355 окт  1 09:53 oecore-pxc-image-base-x86_64-aarch64-am64xx-evm-toolchain-2024.8-host.spdx.tar.zst
-rwxr-xr-x 2 <USER> <GROUP> 1540644192 окт  1 10:10 oecore-pxc-image-base-x86_64-aarch64-am64xx-evm-toolchain-2024.8.sh*
-rw-r--r-- 2 <USER> <GROUP> 56047 окт  1 09:52 oecore-pxc-image-base-x86_64-aarch64-am64xx-evm-toolchain-2024.8.target.manifest
-rw-r--r-- 2 <USER> <GROUP> 8780492 окт  1 09:52 oecore-pxc-image-base-x86_64-aarch64-am64xx-evm-toolchain-2024.8-target.spdx.tar.zst
-rw-r--r-- 2 <USER> <GROUP> 652359 окт  1 09:52 oecore-pxc-image-base-x86_64-aarch64-am64xx-evm-toolchain-2024.8.testdata.json
```
2.  **Install SDK for am64xx evaluation board**
- Run generated .sh script from sdk folder
```
$ cd tmp/deploy/sdk/
$ ./oecore-pxc-image-base-x86_64-aarch64-am64xx-evm-toolchain-2024.8.sh
```
- Provide a path to install SDK on the host.
For example it could be _/home/<USER>/SDK_test_. Follow the next steps which will be provided by the script !
- After succesfull installation, extracted components will be simmilar like:
```
$:~/SDK_test$ ll
total 44
drwxr-xr-x  5 <USER> <GROUP>  4096 окт  1 10:27 ./
drwxr-x--- 31 <USER> <GROUP>  4096 окт  1 13:40 ../
drwxr-xr-x  2 <USER> <GROUP>  4096 окт  1 10:19 cmake/
-rw-r--r--  1 <USER> <GROUP>  3898 окт  1 10:21 environment-setup-aarch64-oe-linux
-rw-r--r--  1 <USER> <GROUP> 11785 окт  1 10:21 site-config-aarch64-oe-linux
drwxr-xr-x  4 <USER> <GROUP>  4096 окт  1 10:19 sysroots/
-rw-r--r--  1 <USER> <GROUP>  3793 окт  1 10:21 toolchain.cmake
-rw-r--r--  1 <USER> <GROUP>   123 окт  1 10:21 version-aarch64-oe-linux
```
