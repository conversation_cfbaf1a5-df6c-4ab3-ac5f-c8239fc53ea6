stages:
  - build-all
  - deploy

.common_features: &common
  tags:
    - bochko

.standard-rules:       # Make a hidden job to hold the common rules
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == 'web'
    - if: $CI_PIPELINE_SOURCE == 'schedule'

build-all:
  <<: *common
  extends:
    - .standard-rules  # Reuse the configuration in `.standard-rules` here
  stage: build-all
  variables:
    GIT_CLEAN_FLAGS: none
#    GIT_CLONE_PATH: $CI_BUILDS_DIR/$CI_RUNNER_SHORT_TOKEN/$CI_CONCURRENT_ID/$CI_PROJECT_PATH
  image: 
    name: gitlab.festo.company:5050/festo/yocto/ea/controls/infrastructure/crops_poky_container/ubuntu-22.04/crops_poky_container:9b7d5fd7
    entrypoint: [ '/bin/sh', '-c' ]

  before_script:
    - echo "Entering build stage"
    - printenv
    - set -e
    - git config --global credential.helper store
    - echo "https://gitlab-ci-token:${CI_REGISTRY_PASSWORD}@gitlab.festo.company" > ~/.git-credentials
    - git config --global --add safe.directory '*'
    - git lfs install
  script:
    - bash scripts/checkout_sources.sh processor-sdk-am64xx-config-01.00.txt
    - |
        if [ ${CI_PIPELINE_SOURCE} == "schedule" ]; then 
          sed -i'.bak' -e 's/^DL_DIR.*/DL_DIR="\/builds\/downloads"/' -e 's/^SSTATE_DIR.*/SSTATE_DIR="\/builds\/sstate-cache"/' boards/am64xx/local.conf
        fi
    - bash scripts/echo_sep.sh midrange -k pxc-bundle-base
    - bash scripts/build_midrange_pxc.sh
    - bash scripts/echo_sep.sh midrange "-c populate_sdk"
    - MACHINE=am64xx-midrange bash scripts/build_am64xx_sdk_pxc.sh
    - bash scripts/echo_sep.sh midrange-emmc4g -k pxc-bundle-base
    - MACHINE=am64xx-midrange-emmc4g bash scripts/build_midrange_emmc4g.sh
    - bash scripts/echo_sep.sh midrange-usb4g -k flasher-image-base
    - bash scripts/build_midrange_pxc_emmc4g_usb4g.sh
    - bash scripts/echo_sep.sh evm -k pxc-bundle-base
    - bash scripts/build_am64xx_pxc.sh
    - |
        if [ ${CI_DEPLOY_ARTIFACTS} == "yes" ]; then
          pwd
          bash scripts/deploy_cecc-ax.sh
        fi
  dependencies: []
  timeout: 12 hours

deploy_all:
  <<: *common
  extends:
    - .standard-rules  # Reuse the configuration in `.standard-rules` here
  stage: deploy
  script:
    - echo "Deploying images to artifactory"
  dependencies: []
