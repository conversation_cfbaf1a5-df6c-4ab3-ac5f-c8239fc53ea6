{"python.autoComplete.extraPaths": ["${workspaceFolder}/sources/poky/bitbake/lib", "${workspaceFolder}/sources/poky/meta/lib"], "python.analysis.extraPaths": ["${workspaceFolder}/sources/poky/bitbake/lib", "${workspaceFolder}/sources/poky/meta/lib"], "files.associations": {"*.conf": "bitbake", "*.inc": "bitbake"}, "search.exclude": {"**/midrange/.git/**": true, "**/midrange/tisdk/build/**": true, "**/midrange/tisdk/sources/**": true}, "files.watcherExclude": {"**/midrange/.git/**": true, "**/.git/**": true, "**/node_modules/**": true, "**/midrange/tisdk/build/**": true, "**/midrange/tisdk/sources/**": true, "**/*.o": true, "**/*.bin": true, "**/*.out": true}, "C_Cpp.intelliSenseEngine": "Disabled", "C_Cpp.autocomplete": "Disabled", "C_Cpp.loggingLevel": "None", "C_Cpp.files.exclude": {"**/midrange/.git/**": true, "**/midrange/tisdk/build/**": true, "**/midrange/tisdk/sources/**": true, "**/midrange/scripts/**": true}, "typescript.tsserver.maxTsServerMemory": 512, "extensions.autoUpdate": false, "C_Cpp.intelliSenseEngineFallback": "Disabled", "telemetry.enableTelemetry": false, "telemetry.enableCrashReporter": false, "remote.SSH.logLevel": "off", "editor.quickSuggestions": {"other": false, "comments": false, "strings": false}, "editor.formatOnSave": false, "update.mode": "manual"}